import subprocess, threading, time, requests, tkinter as tk
from tkinter import ttk, messagebox

try:
    from Ollama import OllamaManager
except ImportError:
    class OllamaManager:
        @staticmethod
        def download_model(_model_name, _callback=None): return False
        @staticmethod
        def get_installed_models(): return []

class ModelDownloader:
    def __init__(self, status_callback=None, root=None):
        self.status_callback, self.root = status_callback, root
        self.models = ["llama2:7b", "llama2:13b", "mistral:7b", "mixtral:8x7b", "codellama:7b", "codellama:13b", "phi:3", "gemma:2b", "gemma:7b", "neural-chat", "orca-mini", "vicuna", "deepseek-coder:6.7b", "deepseek-llm:7b", "qwen:7b", "qwen:14b", "yi:6b", "yi:34b"]

    def check_ollama_installed(self):
        try: return subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8').returncode == 0
        except: return False

    def check_ollama_running(self):
        try: return requests.get("http://localhost:11434/api/tags", timeout=1).status_code == 200
        except: return False

    def get_installed_models(self):
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=1)
            if response.status_code == 200:
                return [m.get('name') for m in response.json().get("models", []) if m.get('name')]
        except: pass
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                return [line.split()[0] for line in result.stdout.strip().split('\n')[1:] if line.strip()]
        except: pass
        return []

    def get_model_info(self, model):
        descriptions = {"llama2": "Meta's LLM", "mistral": "High-performance", "mixtral": "Mixture of experts", "codellama": "Code-specialized", "phi": "Microsoft's small", "gemma": "Google's lightweight", "neural": "Chat optimized", "orca": "Small assistant", "vicuna": "Fine-tuned LLaMA", "deepseek": "DeepSeek's LLM", "qwen": "Alibaba's LLM", "yi": "01.AI's LLM"}
        sizes = {":7b": "~4GB", ":13b": "~8GB", ":6b": "~3GB", ":3": "~2GB", ":2b": "~1GB", ":14b": "~9GB", ":34b": "~20GB", ":8x7b": "~15GB"}
        desc = next((v for k, v in descriptions.items() if model.startswith(k)), "LLM model")
        size = next((v for k, v in sizes.items() if k in model), "")
        return f"{desc} {size}".strip()

    def download_model(self):
        if not self.check_ollama_installed():
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return
        if not self.check_ollama_running():
            if messagebox.askyesno("Ollama Not Running", "Ollama is not running. Start Ollama now?"):
                from OllamaStarter import OllamaStarter
                OllamaStarter(self.status_callback, self.root).start_ollama()
                if self.status_callback: self.status_callback("Waiting for Ollama...")
                for _ in range(5):
                    time.sleep(1)
                    if self.check_ollama_running(): break
                else: messagebox.showwarning("Warning", "Ollama may not have started properly.")
            else: return
        self._show_model_dialog()

    def _show_model_dialog(self):
        if self.status_callback: self.status_callback("Loading models...")
        installed = self.get_installed_models()
        available = [m for m in self.models if m not in installed] + installed
        self._create_dialog(available, installed)

    def _create_dialog(self, available, installed):
        dialog = tk.Toplevel(self.root)
        dialog.title("Select Model to Download")
        dialog.geometry("600x550")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="Select a Model to Download", font=("Arial", 12, "bold")).pack(pady=10)

        container = ttk.Frame(dialog)
        container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        canvas = tk.Canvas(container, height=300)
        scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview)
        frame = ttk.Frame(canvas)

        frame.bind("<Configure>", lambda _: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        selected = tk.StringVar(value=available[0] if available else "llama2:7b")

        for model in available:
            info = self.get_model_info(model)
            is_installed = model in installed
            if is_installed:
                var = tk.BooleanVar(value=True)
                cb = ttk.Checkbutton(frame, text=f"{model} - {info} [INSTALLED]", variable=var, state='disabled')
                cb.pack(anchor=tk.W, pady=1)
            else:
                ttk.Radiobutton(frame, text=f"{model} - {info}", value=model, variable=selected).pack(anchor=tk.W, pady=1)

        ttk.Separator(dialog, orient='horizontal').pack(fill=tk.X, pady=10)

        custom_frame = ttk.Frame(dialog, padding=10)
        custom_frame.pack(fill=tk.X)
        ttk.Label(custom_frame, text="Or enter custom model:").pack(anchor=tk.W)
        self.custom_model = tk.StringVar()
        ttk.Entry(custom_frame, textvariable=self.custom_model, width=50).pack(fill=tk.X, pady=5)

        buttons = ttk.Frame(dialog, padding=10)
        buttons.pack(fill=tk.X)

        def download():
            model = self.custom_model.get().strip() or selected.get()
            dialog.destroy()
            self._start_download(model)

        ttk.Button(buttons, text="Cancel", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)
        ttk.Button(buttons, text="Download", command=download).pack(side=tk.RIGHT)



    def _start_download(self, model_name):
        display_name = model_name.replace('-', ' ').title()
        if self.status_callback: self.status_callback(f"Downloading {display_name}...")

        self._show_progress_dialog(model_name, display_name)

        def download_thread():
            success = OllamaManager.download_model(model_name, callback=self._progress_callback)
            if self.root:
                if success:
                    self.root.after(0, lambda: self._on_success(display_name))
                else:
                    self.root.after(0, lambda: self._on_failure(model_name))

        threading.Thread(target=download_thread, daemon=True).start()

    def _show_progress_dialog(self, _model_name, display_name):
        self.progress_dialog = tk.Toplevel(self.root)
        self.progress_dialog.title("Downloading Model")
        self.progress_dialog.geometry("400x150")
        self.progress_dialog.transient(self.root)
        self.progress_dialog.grab_set()

        ttk.Label(self.progress_dialog, text=f"Downloading {display_name}", font=("Arial", 10, "bold")).pack(pady=10)

        self.progress_var = tk.StringVar(value="Initializing download...")
        ttk.Label(self.progress_dialog, textvariable=self.progress_var).pack(pady=5)

        self.progress_bar = ttk.Progressbar(self.progress_dialog, mode='indeterminate')
        self.progress_bar.pack(pady=10, padx=20, fill=tk.X)
        self.progress_bar.start()

        ttk.Button(self.progress_dialog, text="Cancel", command=self._cancel_download).pack(pady=5)

    def _progress_callback(self, message):
        if hasattr(self, 'progress_var') and self.progress_var:
            self.root.after(0, lambda: self.progress_var.set(message))

    def _cancel_download(self):
        if hasattr(self, 'progress_dialog'): self.progress_dialog.destroy()

    def _on_success(self, name):
        if hasattr(self, 'progress_dialog'): self.progress_dialog.destroy()
        if self.status_callback: self.status_callback(f"{name} downloaded successfully")
        messagebox.showinfo("Download Complete", f"{name} downloaded successfully!")

    def _on_failure(self, name):
        if hasattr(self, 'progress_dialog'): self.progress_dialog.destroy()
        if self.status_callback: self.status_callback("Download failed")
        messagebox.showerror("Download Failed", f"Failed to download '{name}'.")

def main():
    downloader = ModelDownloader()
    if not downloader.check_ollama_installed(): print("✗ Ollama not installed"); return
    if not downloader.check_ollama_running(): print("✗ Ollama not running"); return
    installed = downloader.get_installed_models()
    print(f"✓ Ollama ready | Models: {len(installed)}")
    for model in installed: print(f"  - {model}")

if __name__ == "__main__": main()
