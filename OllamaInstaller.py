import subprocess, threading, webbrowser
from tkinter import messagebox

try:
    from Ollama import OllamaManager
except ImportError:
    class OllamaManager:
        @staticmethod
        def install_ollama(): return False

class OllamaInstaller:
    def __init__(self, status_callback=None, root=None):
        self.status_callback, self.root = status_callback, root

    def check_ollama_installed(self):
        try: return subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8').returncode == 0
        except: return False

    def install_ollama(self):
        if self.check_ollama_installed():
            messagebox.showinfo("Ollama", "Ollama is already installed.")
            return
        if self.status_callback: self.status_callback("Installing Ollama...")

        def install_thread():
            success = OllamaManager.install_ollama()
            action = self._on_install_success if success else self._on_install_failure
            (self.root.after(0, action) if self.root else action())

        threading.Thread(target=install_thread, daemon=True).start()

    def _on_install_success(self):
        if self.status_callback: self.status_callback("Ollama installed successfully")
        messagebox.showinfo("Ollama", "Ollama has been installed successfully.")

    def _on_install_failure(self):
        webbrowser.open("https://ollama.com/download/windows")
        messagebox.showinfo("Install Ollama", "Please download and install Ollama from the website that just opened.\n\nAfter installation is complete, you may need to restart your computer.")

    @staticmethod
    def get_installation_info():
        installer = OllamaInstaller()
        return {'installed': installer.check_ollama_installed(), 'download_url': 'https://ollama.com/download/windows', 'name': 'Ollama', 'description': 'Local AI model runtime'}

def main():
    installer = OllamaInstaller()
    installed = installer.check_ollama_installed()
    print(f"Ollama: {'✓ Already installed' if installed else '✗ Not installed - Run installer.install_ollama() to install'}")

if __name__ == "__main__": main()
