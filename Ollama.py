import subprocess, time, platform, webbrowser, requests, threading, http.server, socketserver, os

class OllamaManager:
    @staticmethod
    def install_ollama():
        if platform.system() != "Windows":
            webbrowser.open("https://ollama.com/download")
            return False
        try:
            subprocess.run(["curl", "-L", "-o", "OllamaSetup.exe", "https://ollama.com/download/OllamaSetup.exe"], check=True)
            subprocess.run(["start", "/wait", "OllamaSetup.exe"], shell=True, check=True)
            subprocess.Popen(["ollama", "serve"], creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0)
            return True
        except: return False

    @staticmethod
    def start_ollama():
        try:
            if requests.get("http://localhost:11434/api/tags", timeout=1).status_code == 200: return True
        except: pass
        try:
            if platform.system() == "Windows":
                subprocess.Popen(["ollama", "serve"], creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0)
            else:
                subprocess.Popen(["ollama", "serve"])
        except: return False
        for _ in range(10):
            try:
                if requests.get("http://localhost:11434/api/tags", timeout=1).status_code == 200: return True
            except: pass
            time.sleep(1)
        return False

    @staticmethod
    def check_ollama_running():
        try: return requests.get("http://localhost:11434/api/tags", timeout=1).status_code == 200
        except: return False

    @staticmethod
    def get_installed_models():
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=1)
            if response.status_code == 200: return response.json().get("models", [])
        except: pass
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8')
            if result.returncode == 0:
                return [{"name": line.split()[0]} for line in result.stdout.strip().split('\n')[1:] if line.strip()]
        except: pass
        return []

    @staticmethod
    def download_model(model_name, callback=None):
        if callback: callback(f"Downloading {model_name}...")
        try:
            process = subprocess.Popen(['ollama', 'pull', model_name], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1, universal_newlines=True)
            for line in process.stdout:
                if callback: callback(line.strip())
            process.wait()
            if process.returncode == 0:
                if callback: callback(f"Successfully downloaded {model_name}")
                return True
            else:
                if callback: callback(f"Download failed: {process.stderr.read()}")
                return False
        except Exception as e:
            if callback: callback(f"Error: {e}")
            return False

class ProxyHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            html_files = ['Chat_Interface.html', 'deepseek_chat_interface.html', 'web_interface.html', 'Website.html']
            for html_file in html_files:
                if os.path.exists(html_file):
                    with open(html_file, 'rb') as f:
                        content = f.read()
                    self.send_response(200)
                    self.send_header('Content-Type', 'text/html')
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.end_headers()
                    self.wfile.write(content)
                    return
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b"No HTML file found")
        elif self.path.startswith('/api/'):
            try:
                ollama_url = f"http://localhost:11434{self.path[4:]}"
                response = requests.get(ollama_url, timeout=5)
                self.send_response(response.status_code)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(response.content)
            except:
                self.send_response(500)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(b'{"error": "Ollama not available"}')
        else:
            super().do_GET()

    def do_POST(self):
        if self.path.startswith('/api/'):
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            try:
                ollama_url = f"http://localhost:11434{self.path[4:]}"
                response = requests.post(ollama_url, data=post_data, headers={'Content-Type': 'application/json'}, timeout=30)
                self.send_response(response.status_code)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(response.content)
            except:
                self.send_response(500)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(b'{"error": "Ollama not available"}')

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def start_api_server_in_background(_port=8765):
    return None

def start_proxy_server_in_background(port=8766):
    def run_server():
        try:
            with socketserver.TCPServer(("", port), ProxyHandler) as httpd:
                httpd.serve_forever()
        except Exception as e:
            print(f"Failed to start proxy server: {e}")

    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.bind(('', port))
        sock.close()

        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        webbrowser.open(f"http://localhost:{port}/")
        return server_thread
    except:
        html_files = ['Chat_Interface.html', 'deepseek_chat_interface.html', 'web_interface.html', 'Website.html']
        for html_file in html_files:
            if os.path.exists(html_file):
                webbrowser.open("file://" + os.path.abspath(html_file))
                break
        return None

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1].lower() == "start":
        OllamaManager.start_ollama()
    else:
        OllamaManager.start_ollama()
