import subprocess, requests, tkinter as tk
from tkinter import ttk, messagebox

try:
    from Ollama import OllamaManager
except ImportError:
    class OllamaManager:
        @staticmethod
        def get_installed_models(): return []

class ModelStatusChecker:
    def __init__(self, status_callback=None, root=None):
        self.status_callback, self.root = status_callback, root

    def check_ollama_installed(self):
        try: return subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8').returncode == 0
        except: return False

    def check_ollama_running(self):
        try: return requests.get("http://localhost:11434/api/tags", timeout=2).status_code == 200
        except: return False

    def get_installed_models_api(self):
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=1)
            if response.status_code == 200:
                models = []
                for m in response.json().get("models", []):
                    if m.get('name'):
                        size = None
                        if m.get('size'):
                            try: size = float(m.get('size')) / (1024**3)
                            except: pass
                        models.append({'name': m.get('name'), 'size': size, 'modified': m.get('modified_at', 'Unknown')})
                return models
        except: pass
        return []

    def get_installed_models_cli(self):
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8', timeout=2)
            if result.returncode == 0:
                models = []
                for line in result.stdout.strip().split('\n')[1:]:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 2:
                            size = None
                            if 'GB' in parts[1]:
                                try: size = float(parts[1].replace('GB', '').strip())
                                except: pass
                            models.append({'name': parts[0], 'size': size, 'modified': parts[2] if len(parts) > 2 else 'Unknown'})
                return models
        except: pass
        return []

    def check_model_status(self):
        if not self.check_ollama_installed():
            messagebox.showerror("Error", "Ollama is not installed or not in PATH. Please install Ollama first.")
            return
        if self.status_callback: self.status_callback("Checking model status...")

        models = self.get_installed_models_api() if self.check_ollama_running() else self.get_installed_models_cli()
        if self.status_callback: self.status_callback("Ready")
        self._show_model_status_dialog(models or [])

    def _show_model_status_dialog(self, models):
        dialog = tk.Toplevel(self.root)
        dialog.title("Model Status")
        dialog.geometry("700x500")
        dialog.transient(self.root)
        dialog.grab_set()

        title_frame = ttk.Frame(dialog, padding=10)
        title_frame.pack(fill=tk.X)
        ttk.Label(title_frame, text="Installed Models Status", font=("Arial", 14, "bold")).pack(side=tk.LEFT)
        ttk.Button(title_frame, text="Refresh", command=lambda: self._refresh_status(dialog)).pack(side=tk.RIGHT)

        self._create_model_treeview(dialog, models)
        self._add_status_summary(dialog, models)
        ttk.Button(dialog, text="Close", command=dialog.destroy, width=15).pack(side=tk.BOTTOM, pady=(15, 0))

    def _create_model_treeview(self, parent, models):
        frame = ttk.Frame(parent, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)

        tree = ttk.Treeview(frame, columns=('Name', 'Size', 'Modified'), show='headings', height=12)
        tree.heading('Name', text='Model Name')
        tree.heading('Size', text='Size (GB)')
        tree.heading('Modified', text='Last Modified')
        tree.column('Name', width=300)
        tree.column('Size', width=100)
        tree.column('Modified', width=200)

        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        if models:
            for model in models:
                size_str = f"{model['size']:.2f}" if model['size'] else "Unknown"
                tree.insert('', tk.END, values=(model['name'], size_str, model['modified']))
        else:
            tree.insert('', tk.END, values=("No models found", "", ""))

    def _add_status_summary(self, parent, models):
        frame = ttk.Frame(parent, padding=10)
        frame.pack(fill=tk.X)
        total_size = sum(m['size'] for m in models if m['size']) if models else 0
        summary = f"Total Models: {len(models)} | Total Size: {total_size:.2f} GB"
        ttk.Label(frame, text=summary, font=("Arial", 10)).pack()

    def _refresh_status(self, dialog):
        dialog.destroy()
        self.check_model_status()

    @staticmethod
    def get_quick_status():
        checker = ModelStatusChecker()
        if not checker.check_ollama_installed(): return {'ollama_installed': False, 'ollama_running': False, 'models_count': 0, 'total_size': 0}
        models = checker.get_installed_models_api() if checker.check_ollama_running() else checker.get_installed_models_cli()
        return {'ollama_installed': True, 'ollama_running': checker.check_ollama_running(), 'models_count': len(models), 'total_size': sum(m['size'] for m in models if m['size'])}

def main():
    status = ModelStatusChecker.get_quick_status()
    print(f"Ollama: {'✓' if status['ollama_installed'] else '✗'} | Running: {'✓' if status['ollama_running'] else '✗'} | Models: {status['models_count']} | Size: {status['total_size']:.1f} GB")

if __name__ == "__main__": main()
