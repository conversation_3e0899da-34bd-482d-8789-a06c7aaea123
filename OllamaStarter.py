import subprocess, threading, time, requests
from tkinter import messagebox

try:
    from Ollama import OllamaManager
except ImportError:
    class OllamaManager:
        @staticmethod
        def start_ollama(): return False
        @staticmethod
        def check_ollama_running(): return False

class OllamaStarter:
    def __init__(self, status_callback=None, root=None):
        self.status_callback, self.root = status_callback, root

    def check_ollama_installed(self):
        try: return subprocess.run(['ollama', 'list'], capture_output=True, text=True, encoding='utf-8').returncode == 0
        except: return False

    def check_ollama_running(self):
        try: return requests.get("http://localhost:11434/api/tags", timeout=2).status_code == 200
        except: return False

    def start_ollama(self):
        if self.check_ollama_running():
            messagebox.showinfo("Ollama", "Ollama is already running.")
            return
        if not self.check_ollama_installed():
            messagebox.showerror("Error", "<PERSON>lla<PERSON> is not installed or not in PATH. Please install Ollama first.")
            return
        if self.status_callback: self.status_callback("Starting Ollama...")

        def start_thread():
            try:
                success = OllamaManager.start_ollama()
                action = self._on_start_success if success else self._try_fallback_start
                (self.root.after(0, action) if self.root else action())
            except Exception as e:
                error_action = lambda: self._on_start_error(str(e))
                (self.root.after(0, error_action) if self.root else error_action())

        threading.Thread(target=start_thread, daemon=True).start()

    def _try_fallback_start(self):
        try:
            subprocess.Popen(['ollama', 'serve'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
            for _ in range(10):
                try:
                    if requests.get("http://localhost:11434/api/tags", timeout=1).status_code == 200:
                        self._on_start_success()
                        return
                except: pass
                time.sleep(1)
            self._on_start_warning()
        except Exception as e: self._on_start_error(str(e))

    def _on_start_success(self):
        if self.status_callback: self.status_callback("Ollama started successfully")
        messagebox.showinfo("Ollama", "Ollama started successfully.")

    def _on_start_warning(self):
        if self.status_callback: self.status_callback("Failed to start Ollama")
        messagebox.showwarning("Ollama", "Ollama may not have started properly. Please check if Ollama is running in the background.")

    def _on_start_error(self, error_msg):
        if self.status_callback: self.status_callback("Error starting Ollama")
        messagebox.showerror("Error", f"Failed to start Ollama: {error_msg}")

    @staticmethod
    def get_service_status():
        starter = OllamaStarter()
        return {'installed': starter.check_ollama_installed(), 'running': starter.check_ollama_running(), 'service_name': 'Ollama', 'port': 11434, 'api_endpoint': 'http://localhost:11434/api/tags'}

def main():
    status = OllamaStarter.get_service_status()
    print(f"Installed: {status['installed']} | Running: {status['running']}")
    if status['installed'] and not status['running']: print("Ollama is installed but not running. Run starter.start_ollama() to start it")

if __name__ == "__main__": main()
