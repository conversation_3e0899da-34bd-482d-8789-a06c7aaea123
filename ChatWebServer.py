import json,threading,webbrowser
from http.server import <PERSON><PERSON><PERSON><PERSON><PERSON>,BaseHTTPRequestHandler
from urllib.parse import urlparse,parse_qs
from ChatPersistence import ChatPersistence

class ChatWebHandler(BaseHTTPRequestHandler):
    persistence=ChatPersistence()

    def do_GET(self):
        path=urlparse(self.path)
        if path.path=='/':self.serve_html()
        elif path.path=='/api/chats':self.get_chats(path.query)
        else:self.send_error(404)

    def do_POST(self):
        path=urlparse(self.path).path
        if path=='/api/chats/save':self.save_chat()
        elif path=='/api/chats/delete':self.delete_chat()
        else:self.send_error(404)

    def serve_html(self):
        try:
            with open('Chat_Interface.html','r',encoding='utf-8')as f:content=f.read()
            self.send_response(200)
            self.send_header('Content-type','text/html;charset=utf-8')
            self.send_header('Access-Control-Allow-Origin','*')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except:self.send_error(404)

    def get_chats(self,query):
        try:
            model=parse_qs(query).get('model',['default'])[0]
            chats=self.persistence.load_chats(model)
            self.send_json({'chats':chats,'count':len(chats)})
        except:self.send_error(500)

    def save_chat(self):
        try:
            data=json.loads(self.rfile.read(int(self.headers['Content-Length'])).decode('utf-8'))
            model,chat=data.get('model','default'),data.get('chat')
            if not chat or not chat.get('id'):self.send_error(400);return
            success=self.persistence.save_chat(model,chat)
            self.send_json({'success':success})
        except:self.send_error(500)

    def delete_chat(self):
        try:
            data=json.loads(self.rfile.read(int(self.headers['Content-Length'])).decode('utf-8'))
            model,chat_id=data.get('model','default'),data.get('chat_id')
            if not chat_id:self.send_error(400);return
            success=self.persistence.delete_chat(model,chat_id)
            self.send_json({'success':success})
        except:self.send_error(500)

    def send_json(self,data):
        self.send_response(200)
        self.send_header('Content-type','application/json')
        self.send_header('Access-Control-Allow-Origin','*')
        self.end_headers()
        self.wfile.write(json.dumps(data,separators=(',',':')).encode('utf-8'))

    def log_message(self,format,*args):pass

class ChatWebServer:
    def __init__(self,port=8765):
        self.port,self.server=port,None

    def start(self):
        try:
            self.server=HTTPServer(('localhost',self.port),ChatWebHandler)
            threading.Thread(target=self.server.serve_forever,daemon=True).start()
            print(f"Server:http://localhost:{self.port}")
            return True
        except:return False

    def stop(self):
        if self.server:self.server.shutdown();self.server.server_close()

def main():
    s=ChatWebServer()
    if s.start():webbrowser.open(f'http://localhost:{s.port}');input("Enter to stop...");s.stop()

if __name__=="__main__":main()
