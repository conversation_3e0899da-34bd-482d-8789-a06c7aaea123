import json,time
from pathlib import Path

class ChatPersistence:
    def __init__(self,base_dir="Previous_Chats"):
        self.base_dir=Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)

    def get_model_dir(self,model):
        model_dir=self.base_dir/model.replace(":","_").replace("/","_")
        model_dir.mkdir(exist_ok=True)
        return model_dir

    def save_chat(self,model,chat_data):
        try:
            if not chat_data.get('messages'):return False
            chat_data['timestamp']=chat_data.get('timestamp',int(time.time()*1000))
            chat_file=self.get_model_dir(model)/f"{chat_data['id']}.json"
            with open(chat_file,'w',encoding='utf-8')as f:json.dump(chat_data,f,separators=(',',':'))
            return True
        except:return False

    def load_chats(self,model):
        try:
            chats=[]
            for f in self.get_model_dir(model).glob("*.json"):
                try:
                    with open(f,'r',encoding='utf-8')as file:
                        data=json.load(file)
                        if data.get('messages'):chats.append(data)
                except:pass
            return sorted(chats,key=lambda x:x.get('timestamp',0),reverse=True)
        except:return[]

    def delete_chat(self,model,chat_id):
        try:
            chat_file=self.get_model_dir(model)/f"{chat_id}.json"
            if chat_file.exists():chat_file.unlink();return True
            return False
        except:return False

if __name__=="__main__":
    p=ChatPersistence()
    test={"id":"test_123","title":"Test","model":"llama3.2","messages":[{"role":"user","content":"Hello"}]}
    print(f"Save:{p.save_chat('llama3.2',test)}")
    print(f"Load:{len(p.load_chats('llama3.2'))}chats")
    print(f"Delete:{p.delete_chat('llama3.2','test_123')}")
    print("Ready!")
